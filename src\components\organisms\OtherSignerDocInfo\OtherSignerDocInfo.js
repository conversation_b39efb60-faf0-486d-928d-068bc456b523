import {React, useState } from 'react';
import toCapitalize from '@/utils/toCapitalize';
import { choices } from '@/tokens/index';
import filterIp from '@/utils/filterIp';
import dayjs from 'dayjs';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';
import downloadPDF from '@/utils/downloadPDF';
import { downloadFileB64 } from '@/services/user';
import PropTypes from 'prop-types';
import Button from '@/components/atoms/Button';
import Card from '@/components/atoms/Card';
import Icon from '@/components/atoms/Icon';
import DocPreview from '@/components/organisms/DocPreview';
import styles from './OtherSignerDocInfo.module.css';

import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
function OtherSignerDocInfo({ docs, onAccept }) {
  // estado para el checkbox de confirmación de la información registrada en oris
  const [datosConfirmados, setDatosConfirmados] = useState(false);
  // estado para mostrar el mensaje de advertencia
  const [showCheckboxWarning, setShowCheckboxWarning] = useState(false);
  const download = ({ idArchivo, b64, nombreArchivo }) => () => {
    if(b64===null){
      downloadFileB64(idArchivo)
      .then((response) => {
        if (response.status === 200) {
          downloadPDF(response.data.data, nombreArchivo);
        } else {
          enqueueSnackbar(response.data.mensaje, { variant: 'error' });
        }
      })
      .catch((err) => console.error(err));
    }else{
    downloadPDF(b64, nombreArchivo);
  }
  };
  const handleAcceptClick = () => {
    // Verifica si la tabla de datos está visible Y si el checkbox no está marcado.
    if (docs?.firmanteRegistrado && !datosConfirmados) {
      // Si la condición se cumple, muestra el mensaje de advertencia y detiene el proceso.
      setShowCheckboxWarning(true);
      return;
    }
    // si no se cumple la condición, procede con la acción de aceptar
    setShowCheckboxWarning(false);
    onAccept();
  };
  return (
    <>
      <Heading>
        Solicitud de firma de documentos
      </Heading>
      <Paragraph size="sm">
        El solicitante definido a continuación requiere de tu firma
        electrónica
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <table className={styles['user-info']}>
        <tbody>
          <tr>
            <th>Nombre</th>
            <td>
              {toCapitalize(docs?.solicitante)}
            </td>
          </tr>
          <tr>
            <th>Correo electrónico</th>
            <td>
              {docs?.email}
            </td>
          </tr>
        </tbody>
      </table>
      
      {/* tabla de datos del firmante registrado en oris */}
          {docs?.firmanteRegistrado && (
        <>
          <Spacer.Vertical size="lg" />

          {/* Mensaje de advertencia */}
       <div style={{ 
            display: 'flex', 
            alignItems: 'flex-start', 
            gap: '12px', 
            marginBottom: '20px',
            padding: '16px',
            backgroundColor: '#fff3cd',
            border: '1px solid #ffc107',
            borderRadius: '6px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            width: 'fit-content', 
            maxWidth: '100%' 
          }}>
            <Icon name="warning" color="danger" size="md" />
            <div style={{ flex: 1 }}>
              <Paragraph color="danger" size="sm" weight="semibold" style={{ marginBottom: '8px' }}>
                ¡Atención! Verifique su Información
              </Paragraph>
              <Paragraph size="xs" style={{ marginBottom: '8px' }}>
                Antes de continuar, confirme que sus datos personales sean correctos.
                En caso de cualquier inconsistencia, comuníquese con la persona o entidad que solicitó la firma.
              </Paragraph>
            </div>
          </div>

          <Heading size="sm">Confirma tus datos personales</Heading>
          <Paragraph size="sm">
            Verifica que los siguientes datos sean correctos antes de proceder con la firma
          </Paragraph>
          <Spacer.Vertical size="sm" />
          <table className={styles['user-info']}>
            <tbody>
              <tr>
                <th>Nombre</th>
                <td>{toCapitalize(docs?.nombreFirmante)}</td>
              </tr>
              <tr>
                <th>Número de documento</th>
                <td>{docs?.numeroDocumentoFirmante}</td>
              </tr>
              <tr>
                <th>Correo electrónico</th>
                <td>{docs?.correoElectronicoFirmante}</td>
              </tr>
              <tr>
                <th>Número de celular</th>
                <td>{docs?.numeroCelularFirmante}</td>
              </tr>
            </tbody>
          </table>
          <Spacer.Vertical size="sm" />
          
          {/*  checkbox de confirmación */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <input
              type="checkbox"
              checked={datosConfirmados}
              onChange={(e) => {
                const checked = e.target.checked; 
                setDatosConfirmados(e.target.checked);
                if (checked) {
                  setShowCheckboxWarning(false);
                }
              }}
              id="confirmar-datos"
              style={{ marginRight: '8px' }}
            />
            <label htmlFor="confirmar-datos" style={{ cursor: 'pointer' }}>
              <Paragraph size="sm">
                Confirmo que los datos mostrados son correctos
              </Paragraph>
            </label>
          </div>
        </>
      )}
      <Spacer.Vertical size="lg" />
      <Heading size="sm">Documentos pendientes de firma</Heading>
      <Spacer.Vertical size="sm" />
      <div className={styles['grid-card']}>
        {Object.keys(docs?.archivos || {}).map((datos) => (
          <Card
            border="secondary"
            color="base"
            key={`${docs?.archivos[datos]?.ip}-${docs?.archivos[datos]?.nombreArchivo}`}
            style={{ padding: 0, background: choices.color.gray[100] }}
          >
            <div className={styles['card-header']}>
              <Heading size="xs" color="inverted">
                {toCapitalize(docs?.archivos[datos]?.nombreArchivo)}
              </Heading>
            </div>
            <DocPreview
              docId={docs?.archivos[datos]?.idArchivo}
              className={styles.preview}
              isFlat
            />
            {/* {docs?.archivos[datos]?.previewB64 ? ( */}
            {/*  <img */}
            {/*    alt="preview" */}
            {/*    src={`data:image/jpg;base64,${docs?.archivos[datos]?.previewB64}`} */}
            {/*    className={styles.preview} */}
            {/*  /> */}
            {/* ) : ( */}
            {/*  <div style={{ */}
            {/*    display: 'flex', */}
            {/*    alignItems: 'center', */}
            {/*    justifyContent: 'center', */}
            {/*    flexDirection: 'column', */}
            {/*    background: '#fff', */}
            {/*    padding: '10px 0', */}
            {/*  }} */}
            {/*  > */}
            {/*    <Icon name="verifyDocument" color="primary" size="2xl" /> */}
            {/*    <Spacer size="sm" /> */}
            {/*    <Paragraph size="sm" color="muted" isCentered> */}
            {/*      No se pudo obtener la vista previa del documento. */}
            {/*    </Paragraph> */}
            {/*  </div> */}
            {/* )} */}
            {/* <Icon name="verifyDocument" color="primary" size="2xl" /> */}
            <div style={{
              padding: '12px 10px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'end',
              flexWrap: 'wrap',
            }}
            >
              <div>
                <div style={{ display: 'flex' }}>
                  <Paragraph size="xs">
                    <strong>IP del solicitante:</strong>
                    {' '}
                    {filterIp(docs?.archivos[datos]?.ip)}
                  </Paragraph>
                </div>
                <div style={{ display: 'flex' }}>
                  <Paragraph size="xs">
                    <strong>Firmas requeridas:</strong>
                    {' '}
                    {docs?.archivos[datos]?.cantidadFirmas}
                  </Paragraph>
                </div>
                <div style={{ display: 'flex' }}>
                  <Paragraph size="xs">
                    <strong>Fecha de carga:</strong>
                    {' '}
                    {dayjs(docs?.archivos[datos]?.fechaRegistroStr).format('DD/MM/YYYY hh:mm a')}
                  </Paragraph>
                </div>
                {!!docs?.archivos[datos]?.descripcion && (
                  <div style={{ display: 'flex' }}>
                    <Paragraph size="xs">
                      <strong>Descripción:</strong>
                      {' '}
                      {docs?.archivos[datos]?.descripcion}
                    </Paragraph>
                  </div>
                )}
              </div>
              <Icon
                name="documentDownload"
                size="md"
                background="muted"
                color="inverted"
                isClickable
                onClick={download(docs?.archivos[datos])}
              />
            </div>
          </Card>
        ))}
      </div>
      <Spacer.Vertical size="sm" />
      {/* 3. Renderizado condicional del mensaje de advertencia. */}
      {showCheckboxWarning && (
        <Paragraph color="red" size="xs" style={{ marginBottom: '1rem', fontWeight: 'bold' }}>
          Debe confirmar que sus datos son correctos para poder continuar.
        </Paragraph>
      )}
      <Button
        onClick={handleAcceptClick}
        isInline

      >
        Aceptar firma de documento
      </Button>
    </>
  );
}

OtherSignerDocInfo.propTypes = {
  docs: PropTypes.shape({
    solicitante: PropTypes.string,
    email: PropTypes.string,
    // datos del firmante registrado en oris
    firmanteRegistrado: PropTypes.bool,
    nombreFirmante: PropTypes.string,
    numeroDocumentoFirmante: PropTypes.string,
    correoElectronicoFirmante: PropTypes.string,
    numeroCelularFirmante: PropTypes.string,
    archivos: PropTypes.shape({
      ip: PropTypes.string,
      nombreArchivo: PropTypes.string,
      previewB64: PropTypes.string,
      cantidadFirmas: PropTypes.number,
      fechaRegistroStr: PropTypes.string,
    }),
  }).isRequired,
  onAccept: PropTypes.func.isRequired,
};

export default OtherSignerDocInfo;
