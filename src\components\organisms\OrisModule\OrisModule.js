import React, { useState, useEffect } from 'react';
import Grid from '@mui/material/Grid';
import OrisAddSignerForm from '@/components/organisms/OrisAddSignerForm';
import OrisAddDocument from '@/components/organisms/OrisAddDocument';
import StateHandler from '@/components/organisms/StateHandler';
import OrisSignersTable from '@/components/organisms/OrisSignersTable';
import Card from '@/components/atoms/Card';
import Spacer from '@/components/layout/Spacer';
import Divider from '@/components/atoms/Divider';
import { solicitarFirmaPlantillas } from '@/services/user';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';

const ROLES_PRIORIDAD = ['Proyectó', 'Revisó', 'Aprobó', 'Firmante'];

export const addNewSigner = ({ signers, setSigners, setIsTableConfirmed = null }) => (signer) => {
  // Agregar el nuevo firmante
  const newSigners = [...signers, signer];

  // Ordenar por prioridad de roles
  const orderedSigners = newSigners.sort((a, b) => {
    const idxA = ROLES_PRIORIDAD.indexOf(a.rol);
    const idxB = ROLES_PRIORIDAD.indexOf(b.rol);
    return idxA - idxB;
  }).map((s, idx) => ({
    ...s,
    orden: idx + 1,
  }));

  setSigners(orderedSigners);

  if (setIsTableConfirmed) {
    setIsTableConfirmed(false);
  }
};

export const onDelete = ({ setSigners, setIsTableConfirmed = null }) => (numeroDocumento) => {
  setSigners((prevSigners) => {
    const filteredSigners = prevSigners.filter(
      (signer) => signer.numeroDocumento !== numeroDocumento,
    );

    return filteredSigners.map((signer, index) => ({
      ...signer,
      orden: index + 1,
    }));
  });

  if (setIsTableConfirmed) {
    setIsTableConfirmed(false);
  }
};

export const handleMoveUp = ({ setSigners, setIsTableConfirmed }) => (index) => {
  setSigners((prevSigners) => {
    if (index <= 0 || index >= prevSigners.length) return prevSigners;

    const newSigners = [...prevSigners];
    [newSigners[index - 1], newSigners[index]] = [newSigners[index], newSigners[index - 1]];

    const updatedSigners = newSigners.map((signer, idx) => ({
      ...signer,
      orden: idx + 1,
    }));

    // Verificación de seguridad
    if (typeof setIsTableConfirmed === 'function') {
      setIsTableConfirmed(false); // Resetear confirmación
    }

    return updatedSigners;
  });
};

export const handleMoveDown = ({ setSigners, setIsTableConfirmed }) => (index) => {
  setSigners((prevSigners) => {
    if (index < 0 || index >= prevSigners.length - 1) return prevSigners;

    const newSigners = [...prevSigners];
    [newSigners[index], newSigners[index + 1]] = [newSigners[index + 1], newSigners[index]];

    const updatedSigners = newSigners.map((signer, idx) => ({
      ...signer,
      orden: idx + 1,
    }));

    // Verificación de seguridad
    if (typeof setIsTableConfirmed === 'function') {
      setIsTableConfirmed(false); // Resetear confirmación
    }

    return updatedSigners;
  });
};

// Función para mover firmante hacia arriba
export const handleDragEnd = ({ setSigners, setIsTableConfirmed }) => (sourceIndex, destinationIndex) => {
  setSigners((prevSigners) => {
    const newSigners = Array.from(prevSigners);
    const [reorderedItem] = newSigners.splice(sourceIndex, 1);
    newSigners.splice(destinationIndex, 0, reorderedItem);

    const updatedSigners = newSigners.map((signer, index) => ({
      ...signer,
      orden: index + 1,
    }));

    // Verificación de seguridad
    if (typeof setIsTableConfirmed === 'function') {
      setIsTableConfirmed(false); // Resetear confirmación
    }

    return updatedSigners;
  });
};

export const handleSubmit = ({
  setState, signers, setSigners, user,
}) => async (formValues) => {
  // Validar firmantes
  if (!signers || signers.length === 0) {
    setState((prev) => ({
      ...prev,
      haveError: true,
      errorMessage: {
        ...prev.errorMessage,
        content: 'Debe agregar al menos 1 firmante',
      },
      isLoading: false,
    }));
    return;
  }

  setState((prev) => ({
    ...prev,
    haveError: false,
    isLoading: true,
  }));

  try {
    let response;

    // Extraer datos del formulario
    const {
      plantillas = [],
      documentos = [],
      tipoOrden: tipoOrdenFirma,
      fechaVigencia,
      descripcion,
      tipoFirma,
    } = formValues;

    // Preparar firmantes con orden
    const firmantesConOrden = signers.map((signer, index) => ({
      email: signer.correoElectronico || signer.email,
      nombreCompleto: signer.nombreCompleto,
      rol: signer.rol || 'Firmante',
      tipoDocumento: signer.tipoDocumento,
      numeroDocumento: signer.numeroDocumento,
      fechaExpedicionDocumento: signer.fechaExpedicionDocumento || signer.fechaExpedicion,
      numeroCelular: signer.numeroCelular,
      orden: tipoOrdenFirma === 'SECUENCIAL' ? index + 1 : 1,
    }));

    // Si hay plantillas, usar el endpoint unificado
    if (plantillas && plantillas.length > 0) {
      const documentosAdicionales = documentos && documentos.length > 0
        ? documentos.map((doc) => ({
          nombreArchivo: doc.nombreArchivo,
          archivo64: doc.archivo64,
          descripcion: doc.descripcion || descripcion,
          cantidadFirmas: signers.length,
        }))
        : undefined;

      const body = {
        idUsuario: user?.idUsuarioC,
        plantillas: plantillas.map((plantilla) => ({
          idPlantilla: plantilla.idPlantilla,
          descripcion: plantilla.descripcion || descripcion,
        })),
        ...(documentosAdicionales && { documentos: documentosAdicionales }),
        tipoOrden: tipoOrdenFirma,
        fechaVigencia,
        descripcionPersonalizada: descripcion,
        tipoFirma,
        firmantes: firmantesConOrden,
      };

      try {
        response = await solicitarFirmaPlantillas(body, user?.access_token);
      } catch (serviceError) {
        console.error('Error en servicio solicitarFirmaPlantillas:', serviceError);
        throw serviceError;
      }
    } else if (documentos && documentos.length > 0) {
      // Solo archivos sin plantillas
      const documentosArchivos = documentos.map((doc) => ({
        nombreArchivo: doc.nombreArchivo,
        archivo64: doc.archivo64,
        descripcion: doc.descripcion || descripcion,
        cantidadFirmas: signers.length,
      }));

      const body = {
        idUsuario: user?.idUsuarioC,
        documentos: documentosArchivos,
        tipoOrden: tipoOrdenFirma,
        fechaVigencia,
        descripcionPersonalizada: descripcion,
        tipoFirma,
        firmantes: firmantesConOrden,
      };

      try {
        response = await solicitarFirmaPlantillas(body, user?.access_token);
      } catch (serviceError) {
        console.error('Error en servicio solicitarFirmaPlantillas:', serviceError);
        throw serviceError;
      }
    } else {
      throw new Error('Debe incluir al menos una plantilla o un documento');
    }

    // VALIDAR que la respuesta exista y tenga la estructura esperada
    if (!response) {
      throw new Error('No se recibió respuesta del servidor');
    }

    // VALIDAR que la respuesta tenga la propiedad status
    if (typeof response.status === 'undefined') {
      console.error('Respuesta sin status:', response);
      throw new Error('Respuesta inválida del servidor');
    }

    // Procesar respuesta exitosa
    if (response.status === 200) {
      setState((prev) => ({
        ...prev,
        haveError: true,
        errorMessage: {
          header: 'Éxito',
          content: 'Solicitud de firma procesada correctamente',
        },
        isLoading: false,
      }));
      setSigners([]);
    } else {
      // Manejar errores del servidor
      let errorMessage = response?.data?.mensaje || 'Error al procesar la solicitud';

      if (response.status === 400) {
        const errorText = typeof response.data === 'string'
          ? response.data
          : (response.data?.error || response.data?.mensaje || '');

        if (errorText.includes('ya está marcado como firmado completo')) {
          errorMessage = 'Error: El archivo ya está marcado como firmado completo y no puede ser procesado nuevamente';
        } else if (errorText.includes('ya tiene todas las firmas requeridas')) {
          errorMessage = 'Error: El archivo ya tiene todas las firmas requeridas y no puede ser firmado nuevamente';
        } else if (errorText.includes('Ya existe un archivo con el mismo contenido')) {
          errorMessage = 'Error al cargar el archivo, el archivo ya existe';
        } else if (errorText) {
          errorMessage = errorText;
        }
      }

      setState((prev) => ({
        ...prev,
        haveError: true,
        errorMessage: {
          ...prev.errorMessage,
          content: errorMessage,
        },
        isLoading: false,
      }));
    }
  } catch (error) {
    console.error('Error en proceso de firma:', error);

    let errorMessage = 'Error al procesar la solicitud de firma';

    // Manejar diferentes tipos de errores
    if (error?.response?.status) {
      // Error con respuesta HTTP
      errorMessage = error.response.data?.mensaje || error.message || errorMessage;

      if (error.response.status === 400) {
        const errorText = typeof error.response.data === 'string'
          ? error.response.data
          : (error.response.data?.error || error.response.data?.mensaje || '');

        if (errorText.includes('ya está marcado como firmado completo')) {
          errorMessage = 'Error: El archivo ya está marcado como firmado completo y no puede ser procesado nuevamente';
        } else if (errorText.includes('ya tiene todas las firmas requeridas')) {
          errorMessage = 'Error: El archivo ya tiene todas las firmas requeridas y no puede ser firmado nuevamente';
        } else if (errorText.includes('Ya existe un archivo con el mismo contenido')) {
          errorMessage = 'Error al cargar el archivo, el archivo ya existe';
        } else if (errorText) {
          errorMessage = errorText;
        }
      }
    } else if (error?.message) {
      // Error sin respuesta HTTP (network, etc.)
      errorMessage = error.message;
    }

    setState((prev) => ({
      ...prev,
      haveError: true,
      errorMessage: {
        ...prev.errorMessage,
        content: errorMessage,
      },
      isLoading: false,
    }));
  }
};

export const initialState = {
  haveError: false,
  isLoading: false,
  errorMessage: {
    header: 'Ha ocurrido un error',
    content: '',
  },
};

export const handleRoleUpdate = ({ setSigners, setIsTableConfirmed }) => (updatedSigners) => {
  setSigners(updatedSigners);

  // Verificación de seguridad
  if (typeof setIsTableConfirmed === 'function') {
    setIsTableConfirmed(false); // Resetear confirmación al cambiar roles
  }
};

function OrisModule() {
  const [signers, setSigners] = useState([]);
  const [state, setState] = useState(initialState);
  const [user] = useRecoilState(userState);
  const [tipoOrdenFirma, setTipoOrdenFirma] = useState('PARALELO');
  const [isTableConfirmed, setIsTableConfirmed] = useState(false);

  const handleOrdenFirmaChange = (ordenFirma) => {
    setTipoOrdenFirma(ordenFirma);
    setIsTableConfirmed(false);
  };

  const handleTableConfirmationChange = (confirmed) => {
    setIsTableConfirmed(confirmed);
  };

  return (
    <StateHandler
      state={state}
      handleErrorButton={() => setState((prev) => ({ ...prev, haveError: false }))}
    >
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Card>
            <OrisAddSignerForm
              handleCreate={addNewSigner({ signers, setSigners, setIsTableConfirmed })}
              onOrdenFirmaChange={handleOrdenFirmaChange}
            />
            <Divider />
            <Spacer.Vertical size="md" />
            <OrisSignersTable
              handleDelete={onDelete({ setSigners, setIsTableConfirmed })}
              signers={signers}
              handleMoveUp={handleMoveUp({ setSigners, setIsTableConfirmed })}
              handleMoveDown={handleMoveDown({ setSigners, setIsTableConfirmed })}
              handleDragEnd={handleDragEnd({ setSigners, setIsTableConfirmed })}
              handleRoleUpdate={handleRoleUpdate({ setSigners, setIsTableConfirmed })}
              tipoOrdenFirma={tipoOrdenFirma}
              onTableConfirmationChange={handleTableConfirmationChange}
            />
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <OrisAddDocument
              handleSubmit={handleSubmit({
                setState, signers, setSigners, user,
              })}
              signers={signers}
              setSigners={setSigners}
              tipoOrdenFirma={tipoOrdenFirma}
              isTableConfirmed={isTableConfirmed}
              initialFormValues={{
                tipoFirma: 'OTHERS',
                tipoOrden: 'PARALELO',
                fechaVigencia: '',
                descripcion: '',
              }}
            />
          </Card>
        </Grid>
      </Grid>
    </StateHandler>
  );
}

export default OrisModule;
