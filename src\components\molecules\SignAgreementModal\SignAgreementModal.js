import React from 'react';
import PropTypes from 'prop-types';
import Modal from '@/components/atoms/Modal';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';
import Link from '@/components/atoms/Link';

export function SignAgreementModal({ onClose }) {
  return (
    <Modal
      title="ACUERDO DE FIRMA ELECTRÓNICA"
      type="tertiary"
      onClose={onClose}
    >
      <Paragraph size="xs" isJustify>
        El usuario firmante acepta mediante este documento firmar documentos electrónicos por
        medio de firma electrónica, realizar comunicaciones, efectuar transacciones o cualquier otra
        actividad mediante el uso del intercambio electrónico de datos, de acuerdo con lo
        establecido por el Decreto 2364 de 2012
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        De una parte, EL USUARIO INICIADOR DEL PROCESO DE FIRMA ELECTRONICA A TRAVÉS DE LA
        PLATAFORMA FIRMESE, en adelante
        {' '}
        <strong>EL USUARIO INICIADOR</strong>
        {' '}
        y, de otra parte, EL USUARIO FINAL, esto es, la persona que usará los servicios de firma sin
        estar necesariamente registrada
        en la plataforma, han decidido celebrar el presente Acuerdo de Uso de los mecanismos de
        autenticación disponibles en la plataforma de Fírmese como firma electrónica de Documentos
        Electrónicos.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        Para todos los efectos de este acuerdo, se aclara que
        {' '}
        <strong>FÍRMESE</strong>
        {' '}
        es un Software en la nube
        que brinda soluciones de firmado electrónico a sus usuarios.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        Este Software como servicio (SaaS) es propiedad
        {' '}
        <strong>Venleg S.A.S</strong>
        {' '}
        con domicilio
        principal en la Ciudad de Bucaramanga, identificada con NIT 901.922.236-6, tal como
        se acredita en el Certificado de existencia y representación legal expedido por la
        Cámara de Comercio.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        Para cualquier notificación se disponen los siguientes medios de contacto:
      </Paragraph>
      <ul>
        <li>
          <Paragraph size="xs" isJustify>
            <strong>Dirección física: </strong>
            {' '}
            Cra 29 No. 45 - 45 Of 509, Edificio Metropolitan Business
            Park, Bucaramanga (Santander).
          </Paragraph>
        </li>
        <li>
          <Paragraph size="xs" isJustify>
            <strong>Correo electrónico: </strong>
            <Link href="mailto:<EMAIL>" size="xs"><EMAIL></Link>
          </Paragraph>
        </li>
        <li>
          <Paragraph size="xs" isJustify>
            <strong>Teléfono: </strong>
            3234087644
          </Paragraph>
        </li>
      </ul>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        El presente acuerdo se regirá por las siguientes cláusulas:
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>PRIMERA: </strong>
        DEFINICIONES. LAS PARTES convienen la definición de los siguientes términos para
        efectos del presente acuerdo, los cuales podrán ser utilizados en singular y en plural.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>ACUERDO SOBRE EL USO DEL MECANISMO DE FIRMA ELECTRÓNICA: </strong>
        Este es el documento que consigna el Acuerdo de voluntades mediante el cual se estipulan las condiciones
        legales y técnicas a las cuales se ajustarán las partes para firmar Documentos Electrónicos, realizar
        comunicaciones, efectuar transacciones, crear documentos electrónicos o cualquier otra actividad mediante
        el uso del intercambio electrónico de datos.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>USUARIO FINAL: </strong>
        Persona natural o jurídica con la que EL USUARIO INICIADOR establece relaciones de origen legal
        o contractual, para el suministro de productos y/o servicios, en desarrollo de su objeto social.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>FIRMA ELECTRÓNICA. </strong>
        Métodos tales como, códigos, contraseñas, datos biométricos, o claves criptográficas privadas,
        que permiten identificar a una persona, en relación con un mensaje de datos, siempre y cuando
        el mismo sea confiable y apropiado respecto de los fines para los que se utiliza la firma,
        atendidas todas las circunstancias del caso, en virtud del presente acuerdo y que es equivalente
        a la Firma Manuscrita.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>OTP (One-Time Password): </strong>
        Firma electrónica de una persona natural, consistente en una clave única creada a partir de datos
        propios del firmante, que permite identificarlo como firmante y aceptante de un Documento Electrónico,
        válida solo para una autenticación.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>Escáner del código de barras del documento de identificación: </strong>
        La cédula de ciudadanía Colombia cuenta con un código de barras de tipo PDF417
        el cual es único, el cual permite la identificación de la persona.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>SEGUNDA: </strong>
        OBJETO. El presente documento tiene por objeto acordar entre LAS PARTES,
        que con el OTP notificado por correo electrónico y/o SMS, una vez se ingrese el código
        y se acepta el presente acuerdo, se da por finalizado el proceso de firma electrónica.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>TERCERA: </strong>
        EQUIVALENCIA FUNCIONAL DEL DOCUMENTO ELECTRÓNICO FIRMADO CON LOS MÉTODOS DE AUTENTICACIÓN DESCRITOS.
        Todo documento electrónico, siempre que no exija, según la ley una formalidad diferente,
        podrá ser firmado electrónicamente por el Usuario final mediante los métodos descritos,
        teniendo en cuenta que tienen los mismos efectos que su firma manuscrita.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>CUARTA: </strong>
        CUSTODIA DEL DOCUMENTO FIRMADO EN FORMA ELECTRÓNICA: Los documentos firmados Electrónicamente
        serán transmitidos a la plataforma de FÍRMESE, donde se almacenarán por el término de treinta (30) días,
        el almacenamiento del hash del documento para su verificación será ilimitado,
        aplicarán las políticas de seguridad de la información y tratamiento de datos personales establecidos
        por dicha entidad.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>QUINTA: </strong>
        OBLIGACIONES DEL USUARIO EN RELACIÓN A LOS MECANISMOS DE AUTENTICACIÓN. EL USUARIO INICIADOR se compromete a:
        Leer y verificar el Documento Electrónico que se le presente para firma. Mantener actualizados en todo momento
        los datos del celular personal y correo electrónico. Reportar en forma inmediata cualquier circunstancia que pueda
        poner en riesgo la seguridad del OTP, la tenencia sobre su documento de identificación, entre otros.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>SEXTA: </strong>
         NO REPUDIO. Las partes entienden y acuerdan que los mecanismos de autenticación habilitados en la plataforma de
        Fírmese seleccionados para la firma son confiables y seguros, lo que en consecuencia significa que la firma es
        confiable y no modificable y en consecuencia EL USUARIO INICIADOR no podrá repudiarla.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>SÉPTIMA: </strong>
        LEY Y JURISDICCIÓN APLICABLE. LAS PARTES acuerdan que el presente acuerdo se rige por la ley colombiana.
        En consecuencia, cualquier conflicto relacionado con el presente acuerdo se tramitará y resolverá ante
        las autoridades colombianas, en el domicilio del USUARIO INICIADOR, sin perjuicio del uso de los mecanismos
        de arreglo directo.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>OCTAVA: </strong>
        Todo lo convenido producirá efectos frente a todos los documentos que firme u ordene según el caso,
        y tendrá efectos frente a terceros, de conformidad con lo previsto en el artículo 824 del Código de Comercio
        y el Decreto 2364 de 2012.
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>NOVENA </strong>
        DESCRIPCIÓN DEL PROCESO DE FIRMA ELECTRÓNICA. Para la firma electrónica el usuario debe seguir el siguiente proceso:
        1) Al USUARIO FIRMANTE le llegará un correo electrónico con el documento y la URL para ingresar a la plataforma
        2) si no es usuario de Fírmese deberá registrarse para continuar con el proceso de firmado,
        si ya es un usuario activo bastará con ingresar y elegir el documento a firmar, le llegará un código OTP
        notificado por correo electrónico y/o SMS
        3) una vez se ingrese el código y se acepta el acuerdo de firma electrónica, se da por finalizado el proceso de firma electrónica.
      </Paragraph>
    </Modal>
  );
}

SignAgreementModal.propTypes = {
  onClose: PropTypes.func.isRequired,
};

export default SignAgreementModal;
